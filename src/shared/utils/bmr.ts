import { BMRCalculationParams, BMRCalculationResult } from '@/shared/types';

/**
 * 基于Mifflin-St Jeor公式计算BMR
 * 男性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 + 5
 * 女性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 - 161
 */
export function calculateBMR(params: BMRCalculationParams): number {
  const { weight, height, age, gender } = params;
  
  const baseBMR = 10 * weight + 6.25 * height - 5 * age;
  return gender === 'male' ? baseBMR + 5 : baseBMR - 161;
}

/**
 * 活动水平系数
 */
export const ACTIVITY_MULTIPLIERS = {
  sedentary: 1.2,      // 久坐不动
  light: 1.375,        // 轻度活动
  moderate: 1.55,      // 中度活动
  active: 1.725,       // 高度活动
  veryActive: 1.9      // 极高活动
} as const;

/**
 * 计算TDEE (Total Daily Energy Expenditure)
 */
export function calculateTDEE(bmr: number, activityLevel: keyof typeof ACTIVITY_MULTIPLIERS): number {
  return bmr * ACTIVITY_MULTIPLIERS[activityLevel];
}

/**
 * 计算每日卡路里限额（基于减重目标）
 */
export function calculateDailyCalorieLimit(
  tdee: number,
  targetWeightLoss: number, // kg
  targetDays: number
): number {
  // 1kg脂肪 ≈ 7700卡路里
  const totalCalorieDeficit = targetWeightLoss * 7700;
  const dailyDeficit = totalCalorieDeficit / targetDays;
  
  // 确保每日卡路里不低于BMR的80%（安全下限）
  const minCalories = tdee * 0.8;
  const targetCalories = tdee - dailyDeficit;
  
  return Math.max(targetCalories, minCalories);
}

/**
 * 计算三餐卡路里分配
 */
export function calculateMealCalories(
  dailyLimit: number,
  ratios: { breakfast: number; lunch: number; dinner: number }
): { breakfast: number; lunch: number; dinner: number } {
  return {
    breakfast: Math.round((dailyLimit * ratios.breakfast) * 100) / 100,
    lunch: Math.round((dailyLimit * ratios.lunch) * 100) / 100,
    dinner: Math.round((dailyLimit * ratios.dinner) * 100) / 100
  };
}

/**
 * 完整的BMR和卡路里计算
 */
export function calculateNutritionPlan(
  params: BMRCalculationParams & {
    targetWeight: number;
    targetDays: number;
    activityLevel: keyof typeof ACTIVITY_MULTIPLIERS;
    mealRatios?: { breakfast: number; lunch: number; dinner: number };
  }
): BMRCalculationResult {
  const bmr = calculateBMR(params);
  const tdee = calculateTDEE(bmr, params.activityLevel);
  const weightLoss = params.weight - params.targetWeight;
  const dailyCalorieLimit = calculateDailyCalorieLimit(tdee, weightLoss, params.targetDays);
  
  // 默认三餐比例
  const defaultRatios = { breakfast: 0.3, lunch: 0.4, dinner: 0.3 };
  const ratios = params.mealRatios || defaultRatios;
  
  const mealCalories = calculateMealCalories(dailyCalorieLimit, ratios);
  
  // 计算预期减重速度（kg/week）
  const weeklyDeficit = (tdee - dailyCalorieLimit) * 7;
  const weightLossRate = weeklyDeficit / 7700;
  
  return {
    bmr,
    tdee,
    dailyCalorieLimit,
    weightLossRate,
    mealCalories
  };
}

/**
 * 计算推荐体重（与ProfileSetupForm中的算法保持一致）
 */
export function calculateRecommendedWeight(
  currentWeight: number,
  height: number,
  targetDays: number,
  activityLevel: keyof typeof ACTIVITY_MULTIPLIERS
): number {
  const heightInM = height / 100;
  const currentBMI = currentWeight / (heightInM * heightInM);

  // 健康BMI范围：18.5-24.9
  const healthyMinWeight = 18.5 * heightInM * heightInM;
  const healthyMaxWeight = 24.9 * heightInM * heightInM;

  // 活动水平系数（影响减重潜力）
  const activityMultipliers = {
    sedentary: 0.8,     // 久坐：较低减重潜力
    light: 1.0,         // 轻度活动：标准减重潜力
    moderate: 1.2,      // 中度活动：较好减重潜力
    active: 1.4,        // 高度活动：很好减重潜力
    veryActive: 1.6     // 极高活动：最佳减重潜力
  };

  const activityMultiplier = activityMultipliers[activityLevel] || 1.0;

  // 基于目标天数和活动水平计算合理减重量
  const weeksAvailable = targetDays / 7;
  const baseWeeklyLoss = 0.5; // 基础每周减重0.5kg
  const adjustedWeeklyLoss = baseWeeklyLoss * activityMultiplier;
  const totalPossibleLoss = weeksAvailable * adjustedWeeklyLoss;

  // 计算推荐目标体重
  let recommendedWeight: number;

  if (currentBMI > 24.9) {
    // 超重：基于活动水平和时间计算合理目标
    const idealReduction = currentWeight - healthyMaxWeight;
    const timeBasedReduction = Math.min(totalPossibleLoss, idealReduction);
    recommendedWeight = currentWeight - Math.max(timeBasedReduction, 2); // 至少减重2kg
  } else if (currentBMI < 18.5) {
    // 体重不足：推荐增到健康范围
    recommendedWeight = healthyMinWeight;
  } else {
    // 健康范围内：基于活动水平适度优化
    const moderateReduction = Math.min(totalPossibleLoss * 0.7, 5); // 最多减5kg
    recommendedWeight = Math.max(currentWeight - moderateReduction, healthyMinWeight);
  }

  // 确保推荐体重在合理范围内
  recommendedWeight = Math.max(recommendedWeight, healthyMinWeight);
  recommendedWeight = Math.min(recommendedWeight, currentWeight); // 不超过当前体重

  // 保持2位小数精度
  return Math.round(recommendedWeight * 100) / 100;
}

/**
 * 验证减重目标是否安全（修改为不阻止保存，与推荐体重一致时跳过验证）
 */
export function validateWeightLossGoal(
  currentWeight: number,
  targetWeight: number,
  targetDays: number,
  height?: number,
  activityLevel?: keyof typeof ACTIVITY_MULTIPLIERS
): { isValid: boolean; message?: string; isWarning?: boolean } {
  // 如果提供了身高和活动水平，检查是否与推荐体重一致
  if (height && activityLevel) {
    const recommendedWeight = calculateRecommendedWeight(currentWeight, height, targetDays, activityLevel);
    // 如果目标体重与推荐体重一致（允许0.1kg的误差），直接跳过验证
    if (Math.abs(targetWeight - recommendedWeight) <= 0.1) {
      return { isValid: true };
    }
  }

  const weightLoss = currentWeight - targetWeight;
  const weeklyLoss = (weightLoss / targetDays) * 7;

  if (weightLoss <= 0) {
    return {
      isValid: true, // 修改为不阻止保存
      isWarning: true,
      message: '目标体重应低于当前体重'
    };
  }

  // 所有验证都改为警告级别，不阻止保存
  if (weeklyLoss > 1.2) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度过快，建议每周减重不超过1.2kg'
    };
  }

  if (weeklyLoss < 0.1) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度过慢，建议每周减重至少0.1kg'
    };
  }

  // 添加警告级别的提示（不阻止用户继续）
  if (weeklyLoss > 1.0) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度较快，请注意营养均衡和身体状况'
    };
  }

  if (weeklyLoss < 0.3) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度较慢，可以适当增加运动量'
    };
  }

  return { isValid: true };
}