import React, { useState, useEffect } from 'react';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import { AIModelProvider, AIModelConfig } from '@/shared/types/aiModel';

interface AIModelManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AIModelManagementModal: React.FC<AIModelManagementModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { 
    models, 
    activeModelId, 
    addModel, 
    updateModel, 
    deleteModel, 
    setActiveModel,
    validateModel,
    getModelList 
  } = useAIModelStore();

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingModel, setEditingModel] = useState<AIModelConfig | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: 'openai' as AIModelProvider,
    apiKey: '',
    baseUrl: '',
    modelName: '',
  });
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<string>('');

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      provider: 'openai',
      apiKey: '',
      baseUrl: '',
      modelName: '',
    });
    setAvailableModels([]);
    setValidationResult('');
    setEditingModel(null);
  };

  // 编辑模型
  const handleEditModel = (model: AIModelConfig) => {
    setEditingModel(model);
    setFormData({
      name: model.name,
      provider: model.provider,
      apiKey: model.apiKey,
      baseUrl: model.baseUrl,
      modelName: model.modelName,
    });
    setShowAddForm(true);
  };

  // 获取模型列表
  const handleGetModels = async () => {
    if (!formData.apiKey || !formData.baseUrl) {
      setValidationResult('请先填写API Key和Base URL');
      return;
    }

    setIsValidating(true);
    try {
      const models = await getModelList(formData.provider, formData.apiKey, formData.baseUrl);
      setAvailableModels(models);
      setValidationResult(`成功获取到 ${models.length} 个模型`);
    } catch (error) {
      setValidationResult('获取模型列表失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 验证模型配置
  const handleValidateModel = async () => {
    if (!formData.modelName) {
      setValidationResult('请选择或输入模型名称');
      return;
    }

    setIsValidating(true);
    try {
      const result = await validateModel(formData);
      if (result.isValid) {
        setValidationResult(`✅ 模型验证成功！${result.capabilities?.supportsVision ? ' 支持图像识别' : ' 仅支持文本'}`);
      } else {
        setValidationResult(`❌ 验证失败: ${result.error}`);
      }
    } catch (error) {
      setValidationResult('验证失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 保存模型
  const handleSaveModel = async () => {
    if (!formData.name || !formData.apiKey || !formData.baseUrl || !formData.modelName) {
      setValidationResult('请填写完整的配置信息');
      return;
    }

    setIsValidating(true);
    try {
      const result = await validateModel(formData);
      if (!result.isValid) {
        setValidationResult(`❌ 验证失败: ${result.error}`);
        setIsValidating(false);
        return;
      }

      const modelConfig = {
        ...formData,
        supportsVision: result.capabilities?.supportsVision || false,
        isActive: false,
      };

      if (editingModel) {
        updateModel(editingModel.id, modelConfig);
      } else {
        addModel(modelConfig);
      }

      setValidationResult('✅ 模型保存成功！');
      setTimeout(() => {
        setShowAddForm(false);
        resetForm();
      }, 1000);
    } catch (error) {
      setValidationResult('保存失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsValidating(false);
    }
  };

  // 默认配置
  const getDefaultBaseUrl = (provider: AIModelProvider) => {
    switch (provider) {
      case 'openai':
        return 'https://api.openai.com';
      case 'gemini':
        return 'https://generativelanguage.googleapis.com';
      default:
        return '';
    }
  };

  // 当provider改变时更新baseUrl
  useEffect(() => {
    if (formData.provider) {
      setFormData(prev => ({
        ...prev,
        baseUrl: getDefaultBaseUrl(prev.provider),
      }));
    }
  }, [formData.provider]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">AI模型管理</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {!showAddForm ? (
            // 模型列表视图
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">已配置的模型</h3>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  + 添加模型
                </button>
              </div>

              {models.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-lg mb-4">🤖</div>
                  <p className="text-gray-500 mb-4">还没有配置任何AI模型</p>
                  <button
                    onClick={() => setShowAddForm(true)}
                    className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors"
                  >
                    添加第一个模型
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {models.map((model) => (
                    <div
                      key={model.id}
                      className={`border rounded-xl p-4 ${
                        model.id === activeModelId ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold text-gray-900">{model.name}</h4>
                            {model.id === activeModelId && (
                              <span className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full">
                                当前使用
                              </span>
                            )}
                            {model.supportsVision && (
                              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                支持图像
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>提供商: {model.provider.toUpperCase()}</p>
                            <p>模型: {model.modelName}</p>
                            <p>地址: {model.baseUrl}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {model.id !== activeModelId && (
                            <button
                              onClick={() => setActiveModel(model.id)}
                              className="text-emerald-600 hover:text-emerald-700 text-sm"
                            >
                              设为当前
                            </button>
                          )}
                          <button
                            onClick={() => handleEditModel(model)}
                            className="text-blue-600 hover:text-blue-700 text-sm"
                          >
                            编辑
                          </button>
                          <button
                            onClick={() => deleteModel(model.id)}
                            className="text-red-600 hover:text-red-700 text-sm"
                          >
                            删除
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            // 添加/编辑表单视图
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingModel ? '编辑模型' : '添加新模型'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  返回列表
                </button>
              </div>

              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      模型名称 *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="例如: GPT-4 Vision"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      提供商 *
                    </label>
                    <select
                      value={formData.provider}
                      onChange={(e) => setFormData(prev => ({ ...prev, provider: e.target.value as AIModelProvider }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="openai">OpenAI</option>
                      <option value="gemini">Google Gemini</option>
                    </select>
                  </div>
                </div>

                {/* API配置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API Key *
                  </label>
                  <input
                    type="password"
                    value={formData.apiKey}
                    onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="输入API密钥"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base URL *
                  </label>
                  <input
                    type="url"
                    value={formData.baseUrl}
                    onChange={(e) => setFormData(prev => ({ ...prev, baseUrl: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="API服务地址"
                  />
                </div>

                {/* 模型选择 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      模型名称 *
                    </label>
                    <button
                      onClick={handleGetModels}
                      disabled={isValidating}
                      className="text-sm text-emerald-600 hover:text-emerald-700 disabled:opacity-50"
                    >
                      {isValidating ? '获取中...' : '获取可用模型'}
                    </button>
                  </div>
                  
                  {availableModels.length > 0 ? (
                    <select
                      value={formData.modelName}
                      onChange={(e) => setFormData(prev => ({ ...prev, modelName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="">选择模型</option>
                      {availableModels.map((model) => (
                        <option key={model} value={model}>
                          {model}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type="text"
                      value={formData.modelName}
                      onChange={(e) => setFormData(prev => ({ ...prev, modelName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="手动输入模型名称，如: gpt-4-vision-preview"
                    />
                  )}
                </div>

                {/* 验证结果 */}
                {validationResult && (
                  <div className={`p-3 rounded-lg text-sm ${
                    validationResult.includes('✅') ? 'bg-green-50 text-green-800' : 
                    validationResult.includes('❌') ? 'bg-red-50 text-red-800' : 
                    'bg-blue-50 text-blue-800'
                  }`}>
                    {validationResult}
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="flex gap-3 pt-4">
                  <button
                    onClick={handleValidateModel}
                    disabled={isValidating}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors whitespace-nowrap"
                  >
                    {isValidating ? '验证中...' : '验证配置'}
                  </button>
                  <button
                    onClick={handleSaveModel}
                    disabled={isValidating}
                    className="flex-1 bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 disabled:opacity-50 transition-colors whitespace-nowrap"
                  >
                    {isValidating ? '保存中...' : '保存模型'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIModelManagementModal;
