import React from 'react';
import { formatDecimal } from '@/shared/utils/format';

interface NumberDisplayProps {
  value: number | undefined | null;
  unit?: string;
  className?: string;
  precision?: 'auto' | 'integer' | number;
  fallback?: string;
}

/**
 * 全局数值显示组件
 * 统一处理所有数值的格式化显示，支持单位、精度控制等
 * 
 * @param value - 要显示的数值
 * @param unit - 单位（如 kg, cm, kcal 等）
 * @param className - CSS类名
 * @param precision - 精度控制：
 *   - 'auto': 自动去除无意义的.00（默认）
 *   - 'integer': 强制显示为整数
 *   - number: 指定小数位数
 * @param fallback - 当值为空时的回退显示
 */
export const NumberDisplay: React.FC<NumberDisplayProps> = ({
  value,
  unit,
  className = '',
  precision = 'auto',
  fallback = '0'
}) => {
  // 处理空值
  if (value === null || value === undefined || isNaN(value) || !isFinite(value)) {
    return <span className={className}>{fallback}{unit ? ` ${unit}` : ''}</span>;
  }

  // 根据精度设置格式化数值
  let formattedValue: string;
  
  switch (precision) {
    case 'integer':
      formattedValue = Math.round(value).toString();
      break;
    case 'auto':
      formattedValue = formatDecimal(value);
      break;
    default:
      if (typeof precision === 'number') {
        formattedValue = value.toFixed(precision);
        // 如果指定了小数位数但结果是整数，也可以选择去除.00
        if (precision > 0) {
          formattedValue = formattedValue.replace(/\.?0+$/, '');
        }
      } else {
        formattedValue = formatDecimal(value);
      }
  }

  return (
    <span className={className}>
      {formattedValue}{unit ? ` ${unit}` : ''}
    </span>
  );
};

// 预设的常用数值显示组件
export const WeightDisplay: React.FC<Omit<NumberDisplayProps, 'unit'> & { value: number }> = (props) => (
  <NumberDisplay {...props} unit="kg" />
);

export const HeightDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number }> = (props) => (
  <NumberDisplay {...props} unit="cm" precision="integer" />
);

export const AgeDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number }> = (props) => (
  <NumberDisplay {...props} unit="岁" precision="integer" />
);

export const CalorieDisplay: React.FC<Omit<NumberDisplayProps, 'unit'> & { value: number }> = (props) => (
  <NumberDisplay {...props} unit="kcal" />
);

export const BMIDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number }> = (props) => (
  <NumberDisplay {...props} precision={2} />
);

export const DaysDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number }> = (props) => (
  <NumberDisplay {...props} unit="天" precision="integer" />
);

export default NumberDisplay;
