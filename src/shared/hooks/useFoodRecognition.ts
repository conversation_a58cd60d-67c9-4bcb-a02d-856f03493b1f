import { useState, useCallback } from 'react';
import { MealType } from '@/shared/types';
import { useToast } from '@/shared/hooks/useToast';
import { geminiService } from '@/infrastructure/ai/geminiService';

export interface FoodRecognitionResult {
  method: 'text' | 'image';
  meal: MealType;
  content: string | undefined;
  timestamp: string;
  foods: Array<{
    name: string;
    calories: number;
    quantity: string;
    timestamp: string;
    weight: number;       // 重量(g)
    confidence: number;   // 识别置信度(0-1)
    nutrition: {
      protein: number;    // 蛋白质(g)
      fat: number;        // 脂肪(g)
      carbs: number;      // 碳水化合物(g)
      fiber: number;      // 纤维(g)
      sugar: number;      // 糖分(g)
      sodium: number;     // 钠(mg)
    };
  }>;
}

export interface FoodRecognitionState {
  isProcessing: boolean;
  processingStep: string;
  error: string | null;
}

export const useFoodRecognition = () => {
  const [state, setState] = useState<FoodRecognitionState>({
    isProcessing: false,
    processingStep: '',
    error: null
  });

  const { showSuccess, showError, showInfo } = useToast();

  const updateState = useCallback((updates: Partial<FoodRecognitionState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const startRecognition = useCallback(async (
    method: 'text' | 'image',
    selectedMeal: MealType,
    textInput: string,
    selectedImage: File | null,
    onComplete: (result: FoodRecognitionResult) => void
  ) => {
    updateState({
      isProcessing: true,
      error: null,
      processingStep: '正在连接AI服务...'
    });

    try {
      // 检查AI服务配置
      if (!geminiService.isConfigured()) {
        throw new Error('AI服务未配置，请检查API密钥');
      }

      let aiResult: any;

      if (method === 'text') {
        // 文字识别
        updateState({ processingStep: '正在分析食物描述...' });
        aiResult = await geminiService.analyzeTextFood(textInput);
      } else {
        // 图像识别
        if (!selectedImage) {
          throw new Error('请选择要识别的图片');
        }
        updateState({ processingStep: '正在分析食物图片...' });
        aiResult = await geminiService.recognizeFood(selectedImage);
      }

      updateState({ processingStep: '正在生成营养记录...' });

      // 转换AI结果为应用格式
      const foods = aiResult.foods.map((food: any) => ({
        name: food.name,
        calories: food.calories,
        quantity: `${food.weight}g`,
        timestamp: new Date().toISOString(),
        nutrition: food.nutrition || {
          protein: Math.round(food.calories * 0.15 / 4),
          fat: Math.round(food.calories * 0.25 / 9),
          carbs: Math.round(food.calories * 0.6 / 4),
          fiber: Math.round(food.calories * 0.05 / 4),
          sugar: Math.round(food.calories * 0.1 / 4),
          sodium: Math.round(food.calories * 0.5)
        },
        confidence: food.confidence,
        weight: food.weight
      }));

      const totalCalories = foods.reduce((sum: number, food: any) => sum + food.calories, 0);

      const result: FoodRecognitionResult = {
        method,
        meal: selectedMeal,
        content: method === 'text' ? textInput : selectedImage?.name,
        timestamp: new Date().toISOString(),
        foods: foods
      };

      showSuccess(`识别成功！发现${foods.length}种食物，共${totalCalories}卡路里`);
      onComplete(result);

    } catch (err) {
      console.error('AI识别失败:', err);
      let errorMessage = '识别失败，请重试';

      if (err instanceof Error) {
        if (err.message.includes('API密钥')) {
          errorMessage = 'AI服务配置错误，请联系管理员';
        } else if (err.message.includes('超时')) {
          errorMessage = '网络超时，请检查网络连接后重试';
        } else if (err.message.includes('API请求失败')) {
          errorMessage = 'AI服务暂时不可用，请稍后重试';
        } else {
          errorMessage = err.message;
        }
      }

      updateState({ error: errorMessage });
      // 移除Toast重复提示，错误信息已在界面中显示
    } finally {
      updateState({
        isProcessing: false,
        processingStep: ''
      });
    }
  }, [updateState, showSuccess, showError]);

  const stopRecognition = useCallback(() => {
    updateState({
      isProcessing: false,
      processingStep: '',
      error: null
    });
    showInfo('识别已终止');
  }, [updateState, showInfo]);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  return {
    state,
    startRecognition,
    stopRecognition,
    clearError
  };
};
